package com.adins.esign.webservices.test.endpoint;

import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.esign.job.AttachMeteraiJob;
import com.adins.esign.job.StampingOnPremPaymentReceiptJob;
import com.adins.esign.job.StampingPaymentReceiptJob;

import io.swagger.annotations.Api;

@Component
@Path("/test/job-trigger")
@Api(value = "TestJobTriggerService")
@Produces({ MediaType.APPLICATION_JSON })
public class TestJobTriggerEndpoint {

    private static final Logger LOG = LoggerFactory.getLogger(TestJobTriggerEndpoint.class);

    @Autowired
    private AttachMeteraiJob attachMeteraiJob;

    @Autowired
    private StampingOnPremPaymentReceiptJob stampingOnPremPaymentReceiptJob;

    @Autowired
    private StampingPaymentReceiptJob stampingPaymentReceiptJob;

    @GET
    @Path("/triggerAttachMeteraiPajakku")
    public Response triggerAttachMeteraiPajakku() {
        try {
            LOG.info("Manual trigger: triggerAttachMeteraiPajakku started");
            attachMeteraiJob.runAttachMeteraiPajakku();
            LOG.info("Manual trigger: triggerAttachMeteraiPajakku completed");
            return Response.ok("{\"status\":\"success\",\"message\":\"triggerAttachMeteraiPajakku executed successfully\"}").build();
        } catch (Exception e) {
            LOG.error("Error in manual trigger triggerAttachMeteraiPajakku", e);
            return Response.status(500)
                    .entity("{\"status\":\"error\",\"message\":\"" + e.getMessage() + "\"}")
                    .build();
        }
    }

    @GET
    @Path("/triggerStampingOnPremPaymentReceipt")
    public Response triggerStampingOnPremPaymentReceipt() {
        try {
            LOG.info("Manual trigger: triggerStampingOnPremPaymentReceipt started");
            stampingOnPremPaymentReceiptJob.runStampingOnPremPaymentReceipt();
            LOG.info("Manual trigger: triggerStampingOnPremPaymentReceipt completed");
            return Response.ok("{\"status\":\"success\",\"message\":\"triggerStampingOnPremPaymentReceipt executed successfully\"}").build();
        } catch (Exception e) {
            LOG.error("Error in manual trigger triggerStampingOnPremPaymentReceipt", e);
            return Response.status(500)
                    .entity("{\"status\":\"error\",\"message\":\"" + e.getMessage() + "\"}")
                    .build();
        }
    }

    @GET
    @Path("/triggerStampingPaymentReceipt")
    public Response triggerStampingPaymentReceipt() {
        try {
            LOG.info("Manual trigger: triggerStampingPaymentReceipt started");
            stampingPaymentReceiptJob.runStampingPaymentReceipt();
            LOG.info("Manual trigger: triggerStampingPaymentReceipt completed");
            return Response.ok("{\"status\":\"success\",\"message\":\"triggerStampingPaymentReceipt executed successfully\"}").build();
        } catch (Exception e) {
            LOG.error("Error in manual trigger triggerStampingPaymentReceipt", e);
            return Response.status(500)
                    .entity("{\"status\":\"error\",\"message\":\"" + e.getMessage() + "\"}")
                    .build();
        }
    }

    @GET
    @Path("/status")
    public Response getStatus() {
        return Response.ok("{\"status\":\"active\",\"message\":\"Test job trigger endpoints are available for the 3 main methods\"}").build();
    }
}
